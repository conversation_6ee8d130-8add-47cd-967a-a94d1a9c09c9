# Motan Consumer ????
# ???CAF???????Motan??????

# ??????
spring.application.name=motan-consumer-native
server.port=8081

# ????
logging.level.com.example.motan=INFO
logging.level.com.weibo.api.motan=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Motan ??????
motan.registry.address=127.0.0.1:2181

# Motan ??????
motan.referer.group=default_rpc
motan.referer.version=1.0
motan.referer.application=motan-consumer-native

# ===== ??????? =====

# ????
# motan.protocol.request-timeout=5000
# motan.registry.connect-timeout=5000
# motan.registry.request-timeout=3000

# ????
# motan.protocol.heartbeat=60000

# ?????
# motan.protocol.serialization=hessian2

# ????
# motan.protocol.compress=false

# ??????
# motan.referer.loadbalance=activeWeight
# ???: random, roundrobin, activeWeight, consistent, localFirst

# ?????
# motan.referer.ha-strategy=failover
# ???: failover, failfast, failback

# ??????
# motan.referer.cluster=failover
# ???: failover, failfast, failback, forking

# ????
# motan.referer.retries=2

# ????
# motan.referer.actives=0

# ??????
# motan.referer.async=false

# ????
# motan.referer.callback=false

# ?????
# motan.referer.min-client-connection=2
# motan.referer.max-client-connection=10

# ??????
# motan.referer.share-channel=true

# ????????
# motan.referer.access-log=false

# ?????????
# motan.referer.check=false

# ???????
# motan.referer.lazy=false

# ????????
# motan.registry.registry-session-timeout=60000
# motan.registry.registry-retry-period=30000
# motan.registry.check=false
# motan.registry.register=false
# motan.registry.subscribe=true

# ??????
spring.profiles.active=dev

package com.example.motan.consumer.controller;

import com.example.motan.api.LoginResult;
import com.example.motan.api.User;
import com.example.motan.api.UserNotFoundException;
import com.example.motan.api.UserStats;
import com.example.motan.consumer.service.UserServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器 - 原生Motan配置版本
 * 
 * 提供HTTP接口调用Motan RPC服务
 * 演示原生配置方式下的服务调用
 */
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserServiceClient userServiceClient;
    
    /**
     * 获取用户信息
     */
    @GetMapping("/{userId}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long userId) {
        logger.info("HTTP请求：获取用户信息，userId: {}", userId);
        
        try {
            User user = userServiceClient.getUserById(userId);
            return ResponseEntity.ok(success("获取用户信息成功", user));
        } catch (UserNotFoundException e) {
            return ResponseEntity.ok(error("用户不存在", e.getMessage()));
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return ResponseEntity.ok(error("系统异常", e.getMessage()));
        }
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody CreateUserRequest request) {
        logger.info("HTTP请求：创建用户，username: {}", request.getUsername());
        
        try {
            Long userId = userServiceClient.createUser(
                request.getUsername(), 
                request.getEmail(), 
                request.getPhone(), 
                request.getAge()
            );
            return ResponseEntity.ok(success("用户创建成功", userId));
        } catch (Exception e) {
            logger.error("创建用户失败", e);
            return ResponseEntity.ok(error("创建用户失败", e.getMessage()));
        }
    }
    
    /**
     * 获取所有用户
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllUsers() {
        logger.info("HTTP请求：获取所有用户");
        
        try {
            List<User> users = userServiceClient.getAllUsers();
            return ResponseEntity.ok(success("获取用户列表成功", users));
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ResponseEntity.ok(error("获取用户列表失败", e.getMessage()));
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    public ResponseEntity<Map<String, Object>> updateUser(
            @PathVariable Long userId, 
            @RequestBody UpdateUserRequest request) {
        logger.info("HTTP请求：更新用户信息，userId: {}", userId);
        
        try {
            boolean success = userServiceClient.updateUser(
                userId, 
                request.getEmail(), 
                request.getPhone(), 
                request.getAddress()
            );
            
            if (success) {
                return ResponseEntity.ok(success("用户信息更新成功", null));
            } else {
                return ResponseEntity.ok(error("用户信息更新失败", "用户不存在或更新失败"));
            }
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return ResponseEntity.ok(error("更新用户信息失败", e.getMessage()));
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable Long userId) {
        logger.info("HTTP请求：删除用户，userId: {}", userId);
        
        try {
            boolean success = userServiceClient.deleteUser(userId);
            
            if (success) {
                return ResponseEntity.ok(success("用户删除成功", null));
            } else {
                return ResponseEntity.ok(error("用户删除失败", "用户不存在"));
            }
        } catch (Exception e) {
            logger.error("删除用户失败", e);
            return ResponseEntity.ok(error("删除用户失败", e.getMessage()));
        }
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody LoginRequest request) {
        logger.info("HTTP请求：用户登录，username: {}", request.getUsername());
        
        try {
            LoginResult result = userServiceClient.login(request.getUsername(), request.getPassword());
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(success("登录成功", result));
            } else {
                return ResponseEntity.ok(error("登录失败", result.getMessage()));
            }
        } catch (Exception e) {
            logger.error("用户登录失败", e);
            return ResponseEntity.ok(error("登录失败", e.getMessage()));
        }
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/{userId}/stats")
    public ResponseEntity<Map<String, Object>> getUserStats(@PathVariable Long userId) {
        logger.info("HTTP请求：获取用户统计信息，userId: {}", userId);
        
        try {
            UserStats stats = userServiceClient.getUserStats(userId);
            return ResponseEntity.ok(success("获取用户统计信息成功", stats));
        } catch (UserNotFoundException e) {
            return ResponseEntity.ok(error("用户不存在", e.getMessage()));
        } catch (Exception e) {
            logger.error("获取用户统计信息失败", e);
            return ResponseEntity.ok(error("获取用户统计信息失败", e.getMessage()));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        logger.info("HTTP请求：健康检查");
        
        try {
            String result = userServiceClient.healthCheck();
            return ResponseEntity.ok(success("健康检查成功", result));
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ResponseEntity.ok(error("健康检查失败", e.getMessage()));
        }
    }
    
    /**
     * 演示批量操作
     */
    @GetMapping("/demo/batch")
    public ResponseEntity<Map<String, Object>> demonstrateBatch() {
        logger.info("HTTP请求：演示批量操作");
        
        try {
            userServiceClient.demonstrateBatchOperations();
            return ResponseEntity.ok(success("批量操作演示完成", "请查看日志"));
        } catch (Exception e) {
            logger.error("批量操作演示失败", e);
            return ResponseEntity.ok(error("批量操作演示失败", e.getMessage()));
        }
    }
    
    /**
     * 演示异常处理
     */
    @GetMapping("/demo/exception")
    public ResponseEntity<Map<String, Object>> demonstrateException() {
        logger.info("HTTP请求：演示异常处理");
        
        try {
            userServiceClient.demonstrateExceptionHandling();
            return ResponseEntity.ok(success("异常处理演示完成", "请查看日志"));
        } catch (Exception e) {
            logger.error("异常处理演示失败", e);
            return ResponseEntity.ok(error("异常处理演示失败", e.getMessage()));
        }
    }
    
    // ===== 辅助方法 =====
    
    private Map<String, Object> success(String message, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        result.put("data", data);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    private Map<String, Object> error(String message, String detail) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("detail", detail);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    // ===== 请求对象 =====
    
    public static class CreateUserRequest {
        private String username;
        private String email;
        private String phone;
        private Integer age;
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
    }
    
    public static class UpdateUserRequest {
        private String email;
        private String phone;
        private String address;
        
        // Getters and Setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
    }
    
    public static class LoginRequest {
        private String username;
        private String password;
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
}

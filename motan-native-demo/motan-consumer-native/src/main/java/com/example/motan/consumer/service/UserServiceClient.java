package com.example.motan.consumer.service;

import com.example.motan.api.*;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 用户服务客户端 - 原生Motan配置
 * 
 * 使用原生@MotanReferer注解，不依赖CAF框架
 * 
 * 注解参数说明：
 * - basicReferer: 引用基础引用配置Bean
 * - group: 服务分组（可选，会覆盖basicReferer中的配置）
 * - version: 服务版本（可选）
 * - protocol: 协议配置（可选）
 * - registry: 注册中心配置（可选）
 * - requestTimeout: 请求超时时间（可选）
 * - retries: 重试次数（可选）
 * - loadbalance: 负载均衡策略（可选）
 * - cluster: 集群容错策略（可选）
 * - async: 是否异步调用（可选）
 */
@Component
public class UserServiceClient {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceClient.class);
    
    /**
     * 注入Motan服务引用 - 使用基础配置
     */
    @MotanReferer(basicReferer = "motanBasicReferer")
    private UserService userService;
    
    /**
     * 也可以直接在注解中指定配置，覆盖基础配置
     * 
     * @MotanReferer(
     *     basicReferer = "motanBasicReferer",
     *     group = "user-service",
     *     version = "1.0",
     *     requestTimeout = 10000,
     *     retries = 3,
     *     loadbalance = "roundrobin",
     *     cluster = "failover"
     * )
     */
    
    /**
     * 获取用户信息
     */
    public User getUserById(Long userId) {
        logger.info("调用远程服务获取用户信息，userId: {}", userId);
        
        try {
            User user = userService.getUserById(userId);
            logger.info("成功获取用户信息: {}", user.getUsername());
            return user;
        } catch (UserNotFoundException e) {
            logger.warn("用户不存在: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("获取用户信息失败，userId: {}", userId, e);
            throw new RuntimeException("服务调用失败", e);
        }
    }
    
    /**
     * 创建用户
     */
    public Long createUser(String username, String email, String phone, Integer age) {
        logger.info("调用远程服务创建用户，username: {}", username);
        
        try {
            User user = new User();
            user.setUsername(username);
            user.setEmail(email);
            user.setPhone(phone);
            user.setAge(age);
            
            Long userId = userService.createUser(user);
            logger.info("用户创建成功，userId: {}", userId);
            return userId;
        } catch (Exception e) {
            logger.error("创建用户失败，username: {}", username, e);
            throw new RuntimeException("创建用户失败", e);
        }
    }
    
    /**
     * 获取所有用户
     */
    public List<User> getAllUsers() {
        logger.info("调用远程服务获取所有用户");
        
        try {
            List<User> users = userService.getAllUsers();
            logger.info("成功获取用户列表，数量: {}", users.size());
            return users;
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            throw new RuntimeException("获取用户列表失败", e);
        }
    }
    
    /**
     * 更新用户信息
     */
    public boolean updateUser(Long userId, String email, String phone, String address) {
        logger.info("调用远程服务更新用户信息，userId: {}", userId);
        
        try {
            User user = new User();
            user.setId(userId);
            user.setEmail(email);
            user.setPhone(phone);
            user.setAddress(address);
            
            boolean success = userService.updateUser(user);
            logger.info("用户信息更新结果: {}", success);
            return success;
        } catch (Exception e) {
            logger.error("更新用户信息失败，userId: {}", userId, e);
            throw new RuntimeException("更新用户信息失败", e);
        }
    }
    
    /**
     * 删除用户
     */
    public boolean deleteUser(Long userId) {
        logger.info("调用远程服务删除用户，userId: {}", userId);
        
        try {
            boolean success = userService.deleteUser(userId);
            logger.info("用户删除结果: {}", success);
            return success;
        } catch (Exception e) {
            logger.error("删除用户失败，userId: {}", userId, e);
            throw new RuntimeException("删除用户失败", e);
        }
    }
    
    /**
     * 批量获取用户信息
     */
    public List<User> batchGetUsers(List<Long> userIds) {
        logger.info("调用远程服务批量获取用户信息，userIds: {}", userIds);
        
        try {
            List<User> users = userService.batchGetUsers(userIds);
            logger.info("批量获取用户完成，请求数量: {}, 返回数量: {}", userIds.size(), users.size());
            return users;
        } catch (Exception e) {
            logger.error("批量获取用户失败，userIds: {}", userIds, e);
            throw new RuntimeException("批量获取用户失败", e);
        }
    }
    
    /**
     * 用户登录
     */
    public LoginResult login(String username, String password) {
        logger.info("调用远程服务进行用户登录，username: {}", username);
        
        try {
            LoginResult result = userService.login(username, password);
            logger.info("登录结果: {}", result.isSuccess() ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            logger.error("用户登录失败，username: {}", username, e);
            throw new RuntimeException("用户登录失败", e);
        }
    }
    
    /**
     * 获取用户统计信息
     */
    public UserStats getUserStats(Long userId) {
        logger.info("调用远程服务获取用户统计信息，userId: {}", userId);
        
        try {
            UserStats stats = userService.getUserStats(userId);
            logger.info("成功获取用户统计信息，userId: {}", userId);
            return stats;
        } catch (UserNotFoundException e) {
            logger.warn("用户不存在: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("获取用户统计信息失败，userId: {}", userId, e);
            throw new RuntimeException("获取用户统计信息失败", e);
        }
    }
    
    /**
     * 健康检查
     */
    public String healthCheck() {
        logger.info("调用远程服务进行健康检查");
        
        try {
            String result = userService.healthCheck();
            logger.info("健康检查结果: {}", result);
            return result;
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            throw new RuntimeException("健康检查失败", e);
        }
    }
    
    /**
     * 演示批量操作
     */
    public void demonstrateBatchOperations() {
        logger.info("=== 演示批量操作 ===");
        
        try {
            // 批量获取用户
            List<Long> userIds = Arrays.asList(1L, 2L, 3L);
            List<User> users = batchGetUsers(userIds);
            
            users.forEach(user -> 
                logger.info("用户信息: ID={}, 用户名={}, 邮箱={}", 
                    user.getId(), user.getUsername(), user.getEmail())
            );
            
        } catch (Exception e) {
            logger.error("批量操作演示失败", e);
        }
    }
    
    /**
     * 演示异常处理
     */
    public void demonstrateExceptionHandling() {
        logger.info("=== 演示异常处理 ===");
        
        try {
            // 尝试获取不存在的用户
            getUserById(999L);
        } catch (UserNotFoundException e) {
            logger.info("捕获到业务异常: {}", e.getMessage());
        } catch (Exception e) {
            logger.error("捕获到系统异常", e);
        }
    }
}

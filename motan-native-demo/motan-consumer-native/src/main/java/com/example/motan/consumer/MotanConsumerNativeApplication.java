package com.example.motan.consumer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Motan服务消费者启动类 - 原生配置版本
 * 
 * 不使用CAF框架的@EnableMotan注解
 * 直接使用Spring Boot + Motan原生配置
 * 
 * 配置方式：
 * 1. 通过@Configuration类定义Motan配置Bean
 * 2. 使用AnnotationBean扫描@MotanReferer注解
 * 3. 通过application.properties配置参数
 */
@SpringBootApplication
public class MotanConsumerNativeApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MotanConsumerNativeApplication.class, args);
        
        System.out.println("=================================");
        System.out.println("Motan Consumer (Native) 启动成功!");
        System.out.println("配置方式: 原生Spring Boot配置");
        System.out.println("Web接口: http://localhost:8081");
        System.out.println("=================================");
    }
}

package com.example.motan.consumer.config;

import com.weibo.api.motan.config.springsupport.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Motan服务消费者原生配置
 * 
 * 不使用CAF脚手架，直接使用Motan官方的Spring Boot集成方式
 * 
 * 核心配置组件：
 * 1. AnnotationBean - 注解扫描配置
 * 2. ProtocolConfigBean - 协议配置
 * 3. RegistryConfigBean - 注册中心配置
 * 4. BasicRefererConfigBean - 服务引用配置
 */
@Configuration
public class MotanConsumerConfig {
    
    @Value("${motan.registry.address:127.0.0.1:2181}")
    private String registryAddress;
    
    @Value("${motan.referer.group:default_rpc}")
    private String refererGroup;
    
    @Value("${motan.referer.version:1.0}")
    private String refererVersion;
    
    @Value("${motan.referer.application:motan-consumer}")
    private String applicationName;
    
    /**
     * 注解扫描配置
     * 用于扫描@MotanReferer注解的服务引用
     */
    @Bean
    public AnnotationBean motanAnnotationBean() {
        AnnotationBean annotationBean = new AnnotationBean();
        // 设置扫描包路径
        annotationBean.setPackage("com.example.motan.consumer.service");
        return annotationBean;
    }
    
    /**
     * 协议配置
     * 定义RPC通信协议和相关参数
     */
    @Bean(name = "motanProtocol")
    public ProtocolConfigBean protocolConfig() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        
        // 协议名称
        config.setName("motan");
        
        // 序列化方式
        config.setSerialization("hessian2");
        
        // 编解码方式
        config.setCodec("motan");
        
        // 网络传输方式
        config.setTransporter("netty");
        
        // 客户端处理器
        config.setEndpointFactory("motan");
        
        // 心跳间隔(毫秒)
        config.setHeartbeat(60000);
        
        // 请求超时时间(毫秒)
        config.setRequestTimeout(5000);
        
        // 最大内容长度
        config.setMaxContentLength(10 * 1024 * 1024);
        
        // 是否开启gzip压缩
        config.setCompress(false);
        
        // 过滤器配置（可选）
        // config.setFilter("trace,accessLog");
        
        return config;
    }
    
    /**
     * 注册中心配置
     * 配置服务注册和发现
     */
    @Bean(name = "motanRegistry")
    public RegistryConfigBean registryConfig() {
        RegistryConfigBean config = new RegistryConfigBean();
        
        // 注册中心地址
        config.setAddress(registryAddress);
        
        // 注册中心协议
        config.setRegProtocol("zookeeper");
        
        // 连接超时时间
        config.setConnectTimeout(5000);
        
        // 请求超时时间
        config.setRequestTimeout(3000);
        
        // 会话超时时间
        config.setRegistrySessionTimeout(60000);
        
        // 重试周期
        config.setRegistryRetryPeriod(30000);
        
        // 是否检查注册中心可用性
        config.setCheck(false);
        
        // 是否注册服务（消费者通常不注册）
        config.setRegister(false);
        
        // 是否订阅服务
        config.setSubscribe(true);
        
        return config;
    }
    
    /**
     * 基础引用配置
     * 定义服务引用的基本参数
     */
    @Bean(name = "motanBasicReferer")
    public BasicRefererConfigBean basicRefererConfig() {
        BasicRefererConfigBean config = new BasicRefererConfigBean();
        
        // 关联协议配置
        config.setProtocol("motanProtocol");
        
        // 关联注册中心配置
        config.setRegistry("motanRegistry");
        
        // 服务分组
        config.setGroup(refererGroup);
        
        // 服务版本
        config.setVersion(refererVersion);
        
        // 应用名称
        config.setApplication(applicationName);
        
        // 模块名称
        config.setModule("consumer");
        
        // 是否共享通道
        config.setShareChannel(true);
        
        // 客户端最大并发数（0表示不限制）
        config.setActives(0);
        
        // 是否异步调用
        config.setAsync(false);
        
        // 是否开启访问日志
        config.setAccessLog(false);
        
        // 是否检查服务提供者
        config.setCheck(false);
        
        // 重试次数
        config.setRetries(2);
        
        // 请求超时时间
        config.setRequestTimeout(5000);
        
        // 负载均衡策略
        config.setLoadbalance("activeWeight");
        
        // 高可用策略
        config.setHaStrategy("failover");
        
        // 集群容错策略
        config.setCluster("failover");
        
        // 连接池配置
        config.setMinClientConnection(2);
        config.setMaxClientConnection(10);
        
        // 是否延迟初始化
        config.setLazy(false);
        
        // 是否启用回调
        config.setCallback(false);
        
        return config;
    }
    
    /**
     * 可选：特定服务的引用配置
     * 如果某个服务需要特殊配置，可以单独定义
     */
    /*
    @Bean(name = "userServiceReferer")
    public BasicRefererConfigBean userServiceRefererConfig() {
        BasicRefererConfigBean config = new BasicRefererConfigBean();
        
        // 继承基础配置
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        config.setGroup(refererGroup);
        config.setVersion(refererVersion);
        config.setApplication(applicationName);
        
        // 特殊配置
        config.setRequestTimeout(10000); // 更长的超时时间
        config.setRetries(3); // 更多重试次数
        config.setLoadbalance("roundrobin"); // 不同的负载均衡策略
        
        return config;
    }
    */
}

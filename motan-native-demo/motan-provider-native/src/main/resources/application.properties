# Motan Provider ????
# ???CAF???????Motan??????

# ??????
spring.application.name=motan-provider-native
server.port=8080

# ????
logging.level.com.example.motan=INFO
logging.level.com.weibo.api.motan=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Motan ??????
motan.registry.address=127.0.0.1:2181

# Motan ??????
motan.protocol.port=8002

# Motan ????
motan.service.group=default_rpc
motan.service.version=1.0
motan.service.application=motan-provider-native

# ===== ??????? =====

# ?????
# motan.protocol.min-worker-thread=20
# motan.protocol.max-worker-thread=200

# ?????
# motan.protocol.serialization=hessian2

# ????
# motan.protocol.request-timeout=5000
# motan.registry.connect-timeout=5000
# motan.registry.request-timeout=3000

# ????
# motan.protocol.heartbeat=60000

# ????
# motan.protocol.compress=false

# ??????
# motan.service.loadbalance=activeWeight
# ???: random, roundrobin, activeWeight, consistent, localFirst

# ?????
# motan.service.ha-strategy=failover
# ???: failover, failfast, failback

# ??????
# motan.service.cluster=default
# ???: default, failover, failfast, failback, forking

# ????
# motan.service.retries=0

# ??????
# motan.service.share-channel=true

# ????????
# motan.service.access-log=false

# ????????
# motan.registry.registry-session-timeout=60000
# motan.registry.registry-retry-period=30000
# motan.registry.check=false
# motan.registry.register=true
# motan.registry.subscribe=true

# ??????
spring.profiles.active=dev

package com.example.motan.provider.config;

import com.weibo.api.motan.config.springsupport.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Motan服务提供者原生配置
 * 
 * 不使用CAF脚手架，直接使用Motan官方的Spring Boot集成方式
 * 
 * 核心配置组件：
 * 1. AnnotationBean - 注解扫描配置
 * 2. ProtocolConfigBean - 协议配置
 * 3. RegistryConfigBean - 注册中心配置
 * 4. BasicServiceConfigBean - 服务暴露配置
 */
@Configuration
public class MotanProviderConfig {
    
    @Value("${motan.registry.address:127.0.0.1:2181}")
    private String registryAddress;
    
    @Value("${motan.protocol.port:8002}")
    private int protocolPort;
    
    @Value("${motan.service.group:default_rpc}")
    private String serviceGroup;
    
    @Value("${motan.service.version:1.0}")
    private String serviceVersion;
    
    @Value("${motan.service.application:motan-provider}")
    private String applicationName;
    
    /**
     * 注解扫描配置
     * 用于扫描@MotanService注解的服务实现类
     */
    @Bean
    public AnnotationBean motanAnnotationBean() {
        AnnotationBean annotationBean = new AnnotationBean();
        // 设置扫描包路径
        annotationBean.setPackage("com.example.motan.provider.service");
        return annotationBean;
    }
    
    /**
     * 协议配置
     * 定义RPC通信协议和相关参数
     */
    @Bean(name = "motanProtocol")
    public ProtocolConfigBean protocolConfig() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        
        // 协议名称
        config.setName("motan");
        
        // 服务端口
        config.setPort(protocolPort);
        
        // 线程池配置
        config.setMinWorkerThread(20);
        config.setMaxWorkerThread(200);
        
        // 序列化方式
        config.setSerialization("hessian2");
        
        // 编解码方式
        config.setCodec("motan");
        
        // 网络传输方式
        config.setTransporter("netty");
        
        // 服务端处理器
        config.setEndpointFactory("motan");
        
        // 心跳间隔(毫秒)
        config.setHeartbeat(60000);
        
        // 请求超时时间(毫秒)
        config.setRequestTimeout(5000);
        
        // 最大内容长度
        config.setMaxContentLength(10 * 1024 * 1024);
        
        // 是否开启gzip压缩
        config.setCompress(false);
        
        // 过滤器配置（可选）
        // config.setFilter("accessLog,trace");
        
        return config;
    }
    
    /**
     * 注册中心配置
     * 配置服务注册和发现
     */
    @Bean(name = "motanRegistry")
    public RegistryConfigBean registryConfig() {
        RegistryConfigBean config = new RegistryConfigBean();
        
        // 注册中心地址
        config.setAddress(registryAddress);
        
        // 注册中心协议
        config.setRegProtocol("zookeeper");
        
        // 连接超时时间
        config.setConnectTimeout(5000);
        
        // 请求超时时间
        config.setRequestTimeout(3000);
        
        // 会话超时时间
        config.setRegistrySessionTimeout(60000);
        
        // 重试周期
        config.setRegistryRetryPeriod(30000);
        
        // 是否检查注册中心可用性
        config.setCheck(false);
        
        // 是否注册服务
        config.setRegister(true);
        
        // 是否订阅服务
        config.setSubscribe(true);
        
        return config;
    }
    
    /**
     * 基础服务配置
     * 定义服务暴露的基本参数
     */
    @Bean(name = "motanBasicService")
    public BasicServiceConfigBean basicServiceConfig() {
        BasicServiceConfigBean config = new BasicServiceConfigBean();
        
        // 关联协议配置
        config.setProtocol("motanProtocol");
        
        // 关联注册中心配置
        config.setRegistry("motanRegistry");
        
        // 服务分组
        config.setGroup(serviceGroup);
        
        // 服务版本
        config.setVersion(serviceVersion);
        
        // 应用名称
        config.setApplication(applicationName);
        
        // 模块名称
        config.setModule("provider");
        
        // 是否共享通道
        config.setShareChannel(true);
        
        // 服务端最大并发数
        config.setActives(0);
        
        // 是否异步
        config.setAsync(false);
        
        // 是否开启访问日志
        config.setAccessLog(false);
        
        // 是否检查服务提供者
        config.setCheck(false);
        
        // 重试次数
        config.setRetries(0);
        
        // 请求超时时间
        config.setRequestTimeout(5000);
        
        // 是否注册服务
        config.setRegister(true);
        
        // 负载均衡策略
        config.setLoadbalance("activeWeight");
        
        // 高可用策略
        config.setHaStrategy("failover");
        
        // 集群容错策略
        config.setCluster("default");
        
        return config;
    }
    
    /**
     * 可选：自定义过滤器配置
     */
    /*
    @Bean
    public FilterConfigBean accessLogFilter() {
        FilterConfigBean filter = new FilterConfigBean();
        filter.setName("accessLog");
        return filter;
    }
    */
}

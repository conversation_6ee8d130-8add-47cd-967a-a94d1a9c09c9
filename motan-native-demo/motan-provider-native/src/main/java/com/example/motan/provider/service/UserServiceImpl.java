package com.example.motan.provider.service;

import com.example.motan.api.*;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 用户服务实现类 - 原生Motan配置
 * 
 * 使用原生@MotanService注解，不依赖CAF框架
 * 
 * 注解参数说明：
 * - basicService: 引用基础服务配置Bean
 * - group: 服务分组（可选，会覆盖basicService中的配置）
 * - version: 服务版本（可选）
 * - export: 暴露协议和端口（可选）
 */
@MotanService(basicService = "motanBasicService")
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    // 模拟数据存储
    private final Map<Long, User> userStorage = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public UserServiceImpl() {
        initTestData();
    }
    
    private void initTestData() {
        User user1 = new User(1L, "admin", "<EMAIL>");
        user1.setAge(30);
        user1.setPhone("13800138000");
        user1.setAddress("北京市朝阳区");
        
        User user2 = new User(2L, "user1", "<EMAIL>");
        user2.setAge(25);
        user2.setPhone("13800138001");
        user2.setAddress("上海市浦东新区");
        
        userStorage.put(1L, user1);
        userStorage.put(2L, user2);
        idGenerator.set(3L);
        
        logger.info("初始化测试数据完成，当前用户数量: {}", userStorage.size());
    }
    
    @Override
    public User getUserById(Long userId) {
        logger.info("获取用户信息，userId: {}", userId);
        
        // 模拟业务处理时间
        simulateProcessingTime(50);
        
        User user = userStorage.get(userId);
        if (user == null) {
            logger.warn("用户不存在，userId: {}", userId);
            throw new UserNotFoundException(userId);
        }
        
        logger.info("成功获取用户信息: {}", user.getUsername());
        return user;
    }
    
    @Override
    public Long createUser(User user) {
        logger.info("创建用户，username: {}", user.getUsername());
        
        // 参数校验
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        // 检查用户名是否已存在
        boolean exists = userStorage.values().stream()
                .anyMatch(u -> u.getUsername().equals(user.getUsername()));
        if (exists) {
            throw new IllegalArgumentException("用户名已存在: " + user.getUsername());
        }
        
        // 生成ID并保存
        Long newId = idGenerator.getAndIncrement();
        user.setId(newId);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setStatus(User.UserStatus.ACTIVE);
        
        userStorage.put(newId, user);
        
        logger.info("用户创建成功，userId: {}, username: {}", newId, user.getUsername());
        return newId;
    }
    
    @Override
    public List<User> getAllUsers() {
        logger.info("获取所有用户列表");
        
        List<User> users = new ArrayList<>(userStorage.values());
        logger.info("返回用户列表，数量: {}", users.size());
        return users;
    }
    
    @Override
    public boolean updateUser(User user) {
        logger.info("更新用户信息，userId: {}", user.getId());
        
        if (user.getId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        User existingUser = userStorage.get(user.getId());
        if (existingUser == null) {
            logger.warn("要更新的用户不存在，userId: {}", user.getId());
            return false;
        }
        
        // 更新字段
        if (user.getUsername() != null) existingUser.setUsername(user.getUsername());
        if (user.getEmail() != null) existingUser.setEmail(user.getEmail());
        if (user.getPhone() != null) existingUser.setPhone(user.getPhone());
        if (user.getAge() != null) existingUser.setAge(user.getAge());
        if (user.getAddress() != null) existingUser.setAddress(user.getAddress());
        if (user.getStatus() != null) existingUser.setStatus(user.getStatus());
        
        existingUser.setUpdateTime(LocalDateTime.now());
        
        logger.info("用户信息更新成功，userId: {}", user.getId());
        return true;
    }
    
    @Override
    public boolean deleteUser(Long userId) {
        logger.info("删除用户，userId: {}", userId);
        
        User removedUser = userStorage.remove(userId);
        boolean success = removedUser != null;
        
        if (success) {
            logger.info("用户删除成功，userId: {}, username: {}", userId, removedUser.getUsername());
        } else {
            logger.warn("要删除的用户不存在，userId: {}", userId);
        }
        
        return success;
    }
    
    @Override
    public List<User> batchGetUsers(List<Long> userIds) {
        logger.info("批量获取用户信息，userIds: {}", userIds);
        
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<User> result = userIds.stream()
                .map(userStorage::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        logger.info("批量获取用户完成，请求数量: {}, 返回数量: {}", userIds.size(), result.size());
        return result;
    }
    
    @Override
    public LoginResult login(String username, String password) {
        logger.info("用户登录，username: {}", username);
        
        // 模拟登录验证
        simulateProcessingTime(100);
        
        User user = userStorage.values().stream()
                .filter(u -> u.getUsername().equals(username))
                .findFirst()
                .orElse(null);
        
        if (user == null) {
            logger.warn("登录失败，用户不存在: {}", username);
            return new LoginResult(false, "用户不存在");
        }
        
        // 简单密码验证
        if (!"password123".equals(password)) {
            logger.warn("登录失败，密码错误: {}", username);
            return new LoginResult(false, "密码错误");
        }
        
        // 生成token
        String token = "token_" + user.getId() + "_" + System.currentTimeMillis();
        
        logger.info("登录成功，username: {}, userId: {}", username, user.getId());
        return new LoginResult(true, token, "登录成功", user);
    }
    
    @Override
    public UserStats getUserStats(Long userId) throws UserNotFoundException {
        logger.info("获取用户统计信息，userId: {}", userId);
        
        User user = userStorage.get(userId);
        if (user == null) {
            throw new UserNotFoundException(userId);
        }
        
        // 模拟统计数据计算
        simulateProcessingTime(200);
        
        UserStats stats = new UserStats();
        stats.setUserId(userId);
        stats.setLoginCount(new Random().nextInt(100) + 1);
        stats.setOrderCount(new Random().nextInt(50));
        stats.setTotalAmount(new Random().nextDouble() * 10000);
        stats.setLastLoginTime(LocalDateTime.now().toString());
        
        logger.info("用户统计信息获取成功，userId: {}", userId);
        return stats;
    }
    
    @Override
    public User getUserByIdAsync(Long userId) {
        logger.info("异步获取用户信息，userId: {}", userId);
        
        // 模拟异步处理
        simulateProcessingTime(300);
        
        return getUserById(userId);
    }
    
    @Override
    public String healthCheck() {
        return "UserService is healthy - " + LocalDateTime.now();
    }
    
    /**
     * 模拟处理时间
     */
    private void simulateProcessingTime(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("处理被中断", e);
        }
    }
}

package com.example.motan.provider;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Motan服务提供者启动类 - 原生配置版本
 * 
 * 不使用CAF框架的@EnableMotan注解
 * 直接使用Spring Boot + Motan原生配置
 * 
 * 配置方式：
 * 1. 通过@Configuration类定义Motan配置Bean
 * 2. 使用AnnotationBean扫描@MotanService注解
 * 3. 通过application.properties配置参数
 */
@SpringBootApplication
public class MotanProviderNativeApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MotanProviderNativeApplication.class, args);
        
        System.out.println("=================================");
        System.out.println("Motan Provider (Native) 启动成功!");
        System.out.println("配置方式: 原生Spring Boot配置");
        System.out.println("服务端口: 8002");
        System.out.println("=================================");
    }
}

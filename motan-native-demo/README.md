# Motan RPC 原生配置示例

这是一个不使用CAF脚手架的Motan RPC原生配置示例，展示如何在Spring Boot项目中直接使用Motan官方的配置方式。

## 项目结构

```
motan-native-demo/
├── motan-api/                           # 服务接口定义（复用之前的）
├── motan-provider-native/               # 服务提供者（原生配置）
│   ├── src/main/java/
│   │   └── com/example/motan/provider/
│   │       ├── config/
│   │       │   └── MotanProviderConfig.java    # 原生配置类
│   │       ├── service/
│   │       │   └── UserServiceImpl.java        # 服务实现
│   │       └── MotanProviderNativeApplication.java
│   ├── src/main/resources/
│   │   └── application.properties              # 配置文件
│   └── pom.xml
└── motan-consumer-native/               # 服务消费者（原生配置）
    ├── src/main/java/
    │   └── com/example/motan/consumer/
    │       ├── config/
    │       │   └── MotanConsumerConfig.java    # 原生配置类
    │       ├── service/
    │       │   └── UserServiceClient.java      # 服务客户端
    │       ├── controller/
    │       │   └── UserController.java         # Web控制器
    │       └── MotanConsumerNativeApplication.java
    ├── src/main/resources/
    │   └── application.properties              # 配置文件
    └── pom.xml
```

## 核心差异对比

### CAF脚手架 vs 原生配置

| 配置方式 | CAF脚手架 | 原生配置 |
|---------|-----------|----------|
| **启动注解** | `@EnableMotan(namespace = "user")` | 无特殊注解 |
| **配置方式** | 自动配置 + 属性绑定 | 手动@Configuration类 |
| **配置前缀** | `app.motan.{namespace}.*` | 自定义属性名 |
| **Bean管理** | 自动注册 | 手动@Bean定义 |
| **命名空间** | 内置支持 | 需要手动实现 |

### 1. 服务提供者配置对比

#### CAF脚手架方式：
```java
@SpringBootApplication
@EnableMotan(namespace = "user")
public class Application {
    // 自动配置，无需额外代码
}
```

```properties
# 配置文件
app.motan.user.annotation.package=com.example.service
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.port=10010
```

#### 原生配置方式：
```java
@Configuration
public class MotanProviderConfig {
    
    @Bean
    public AnnotationBean motanAnnotationBean() {
        AnnotationBean bean = new AnnotationBean();
        bean.setPackage("com.example.motan.provider.service");
        return bean;
    }
    
    @Bean(name = "motanProtocol")
    public ProtocolConfigBean protocolConfig() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        config.setName("motan");
        config.setPort(8002);
        // ... 其他配置
        return config;
    }
    
    @Bean(name = "motanRegistry")
    public RegistryConfigBean registryConfig() {
        RegistryConfigBean config = new RegistryConfigBean();
        config.setAddress("127.0.0.1:2181");
        config.setRegProtocol("zookeeper");
        // ... 其他配置
        return config;
    }
    
    @Bean(name = "motanBasicService")
    public BasicServiceConfigBean basicServiceConfig() {
        BasicServiceConfigBean config = new BasicServiceConfigBean();
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        // ... 其他配置
        return config;
    }
}
```

### 2. 服务消费者配置对比

#### CAF脚手架方式：
```java
@SpringBootApplication
@EnableMotan(namespace = "user")
public class Application {
    // 自动配置
}
```

```properties
app.motan.user.annotation.package=com.example.service
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.basic-referer.request-timeout=20000
```

#### 原生配置方式：
```java
@Configuration
public class MotanConsumerConfig {
    
    @Bean
    public AnnotationBean motanAnnotationBean() {
        AnnotationBean bean = new AnnotationBean();
        bean.setPackage("com.example.motan.consumer.service");
        return bean;
    }
    
    @Bean(name = "motanBasicReferer")
    public BasicRefererConfigBean basicRefererConfig() {
        BasicRefererConfigBean config = new BasicRefererConfigBean();
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        config.setRequestTimeout(5000);
        // ... 其他配置
        return config;
    }
}
```

## 配置详解

### 1. 核心配置Bean

#### AnnotationBean
```java
@Bean
public AnnotationBean motanAnnotationBean() {
    AnnotationBean annotationBean = new AnnotationBean();
    // 设置扫描包路径，用于发现@MotanService和@MotanReferer注解
    annotationBean.setPackage("com.example.motan.provider.service");
    return annotationBean;
}
```

#### ProtocolConfigBean
```java
@Bean(name = "motanProtocol")
public ProtocolConfigBean protocolConfig() {
    ProtocolConfigBean config = new ProtocolConfigBean();
    config.setName("motan");              // 协议名称
    config.setPort(8002);                 // 服务端口
    config.setSerialization("hessian2");  // 序列化方式
    config.setCodec("motan");             // 编解码方式
    config.setTransporter("netty");       // 传输方式
    config.setMinWorkerThread(20);        // 最小工作线程
    config.setMaxWorkerThread(200);       // 最大工作线程
    config.setRequestTimeout(5000);       // 请求超时
    config.setHeartbeat(60000);           // 心跳间隔
    return config;
}
```

#### RegistryConfigBean
```java
@Bean(name = "motanRegistry")
public RegistryConfigBean registryConfig() {
    RegistryConfigBean config = new RegistryConfigBean();
    config.setAddress("127.0.0.1:2181");     // 注册中心地址
    config.setRegProtocol("zookeeper");      // 注册中心协议
    config.setConnectTimeout(5000);          // 连接超时
    config.setRequestTimeout(3000);          // 请求超时
    config.setRegistrySessionTimeout(60000); // 会话超时
    config.setCheck(false);                  // 是否检查可用性
    config.setRegister(true);                // 是否注册服务
    config.setSubscribe(true);               // 是否订阅服务
    return config;
}
```

#### BasicServiceConfigBean（服务提供者）
```java
@Bean(name = "motanBasicService")
public BasicServiceConfigBean basicServiceConfig() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    config.setProtocol("motanProtocol");     // 关联协议配置
    config.setRegistry("motanRegistry");     // 关联注册中心配置
    config.setGroup("default_rpc");          // 服务分组
    config.setVersion("1.0");                // 服务版本
    config.setApplication("motan-provider"); // 应用名称
    config.setShareChannel(true);            // 是否共享通道
    config.setRequestTimeout(5000);          // 请求超时
    config.setLoadbalance("activeWeight");   // 负载均衡策略
    config.setHaStrategy("failover");        // 高可用策略
    return config;
}
```

#### BasicRefererConfigBean（服务消费者）
```java
@Bean(name = "motanBasicReferer")
public BasicRefererConfigBean basicRefererConfig() {
    BasicRefererConfigBean config = new BasicRefererConfigBean();
    config.setProtocol("motanProtocol");     // 关联协议配置
    config.setRegistry("motanRegistry");     // 关联注册中心配置
    config.setGroup("default_rpc");          // 服务分组
    config.setVersion("1.0");                // 服务版本
    config.setApplication("motan-consumer"); // 应用名称
    config.setRequestTimeout(5000);          // 请求超时
    config.setRetries(2);                    // 重试次数
    config.setLoadbalance("activeWeight");   // 负载均衡策略
    config.setCluster("failover");           // 集群容错策略
    config.setMinClientConnection(2);        // 最小连接数
    config.setMaxClientConnection(10);       // 最大连接数
    return config;
}
```

### 2. 服务注解使用

#### 服务提供者
```java
@MotanService(basicService = "motanBasicService")
@Service
public class UserServiceImpl implements UserService {
    // 服务实现
}
```

#### 服务消费者
```java
@Component
public class UserServiceClient {
    
    @MotanReferer(basicReferer = "motanBasicReferer")
    private UserService userService;
    
    // 也可以在注解中覆盖配置
    @MotanReferer(
        basicReferer = "motanBasicReferer",
        group = "user-service",
        version = "1.0",
        requestTimeout = 10000,
        retries = 3
    )
    private UserService specialUserService;
}
```

## 依赖配置

### Maven依赖
```xml
<dependencies>
    <!-- Spring Boot -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
    </dependency>
    
    <!-- Motan核心依赖 -->
    <dependency>
        <groupId>com.weibo</groupId>
        <artifactId>motan-core</artifactId>
        <version>1.1.9</version>
    </dependency>
    
    <dependency>
        <groupId>com.weibo</groupId>
        <artifactId>motan-transport-netty</artifactId>
        <version>1.1.9</version>
    </dependency>
    
    <dependency>
        <groupId>com.weibo</groupId>
        <artifactId>motan-springsupport</artifactId>
        <version>1.1.9</version>
    </dependency>
    
    <dependency>
        <groupId>com.weibo</groupId>
        <artifactId>motan-registry-zookeeper</artifactId>
        <version>1.1.9</version>
    </dependency>
    
    <!-- Zookeeper客户端 -->
    <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>3.4.14</version>
    </dependency>
</dependencies>
```

## 启动步骤

1. **启动Zookeeper**
```bash
zkServer.sh start
```

2. **启动服务提供者**
```bash
cd motan-provider-native
mvn spring-boot:run
```

3. **启动服务消费者**
```bash
cd motan-consumer-native
mvn spring-boot:run
```

4. **测试服务调用**
```bash
curl http://localhost:8081/api/users/1
```

## 优缺点对比

### 原生配置方式

**优点：**
- 配置更加灵活和透明
- 不依赖特定框架，通用性更好
- 可以精确控制每个配置项
- 便于理解Motan的工作原理

**缺点：**
- 配置代码较多，开发效率较低
- 需要手动管理Bean的依赖关系
- 缺少自动化的最佳实践配置
- 多命名空间支持需要额外开发

### CAF脚手架方式

**优点：**
- 开箱即用，配置简单
- 内置最佳实践和优化配置
- 支持多命名空间和配置热更新
- 集成监控和治理功能

**缺点：**
- 依赖特定框架
- 配置透明度较低
- 定制化程度有限

## 适用场景

- **原生配置**：适合对配置有特殊要求、需要深度定制或学习Motan原理的场景
- **CAF脚手架**：适合企业级应用、快速开发和标准化部署的场景

选择哪种方式主要取决于项目需求、团队技术栈和维护成本考虑。

# Motan RPC 实践示例集

本文档提供各种实际场景下的Motan RPC配置和使用示例，帮助您快速应用到实际项目中。

## 目录
1. [基础使用示例](#1-基础使用示例)
2. [高级配置示例](#2-高级配置示例)
3. [性能优化示例](#3-性能优化示例)
4. [故障处理示例](#4-故障处理示例)
5. [监控集成示例](#5-监控集成示例)

## 1. 基础使用示例

### 1.1 简单的用户服务

#### 服务接口定义
```java
public interface UserService {
    User findById(Long id);
    List<User> findByName(String name);
    Long save(User user);
    boolean delete(Long id);
}
```

#### 服务提供者实现
```java
@MotanService(basicService = "motanBasicService")
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }
    
    @Override
    public List<User> findByName(String name) {
        return userRepository.findByNameContaining(name);
    }
    
    @Override
    public Long save(User user) {
        User saved = userRepository.save(user);
        return saved.getId();
    }
    
    @Override
    public boolean delete(Long id) {
        try {
            userRepository.deleteById(id);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 服务消费者使用
```java
@Component
public class UserController {
    
    @MotanReferer(basicReferer = "motanBasicReferer")
    private UserService userService;
    
    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return user != null ? ResponseEntity.ok(user) : ResponseEntity.notFound().build();
    }
    
    @PostMapping("/users")
    public ResponseEntity<Long> createUser(@RequestBody User user) {
        Long id = userService.save(user);
        return ResponseEntity.ok(id);
    }
}
```

### 1.2 配置文件示例

#### 服务提供者配置
```properties
# application.properties
spring.application.name=user-service-provider
server.port=8080

# Motan配置
motan.registry.address=127.0.0.1:2181
motan.protocol.port=8002
motan.service.group=user-service
motan.service.version=1.0
motan.service.application=user-service-provider
```

#### 服务消费者配置
```properties
# application.properties
spring.application.name=user-service-consumer
server.port=8081

# Motan配置
motan.registry.address=127.0.0.1:2181
motan.referer.group=user-service
motan.referer.version=1.0
motan.referer.application=user-service-consumer
```

## 2. 高级配置示例

### 2.1 多版本服务支持

#### 服务提供者 - V1版本
```java
@MotanService(
    basicService = "userServiceV1Config",
    group = "user-service",
    version = "1.0"
)
@Service
public class UserServiceV1Impl implements UserService {
    // V1版本实现
}

@Bean(name = "userServiceV1Config")
public BasicServiceConfigBean userServiceV1Config() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    config.setGroup("user-service");
    config.setVersion("1.0");
    return config;
}
```

#### 服务提供者 - V2版本
```java
@MotanService(
    basicService = "userServiceV2Config",
    group = "user-service",
    version = "2.0"
)
@Service
public class UserServiceV2Impl implements UserService {
    // V2版本实现，可能有新的方法或优化
}

@Bean(name = "userServiceV2Config")
public BasicServiceConfigBean userServiceV2Config() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    config.setGroup("user-service");
    config.setVersion("2.0");
    return config;
}
```

#### 服务消费者 - 版本选择
```java
@Component
public class UserServiceClient {
    
    // 使用V1版本
    @MotanReferer(
        basicReferer = "motanBasicReferer",
        group = "user-service",
        version = "1.0"
    )
    private UserService userServiceV1;
    
    // 使用V2版本
    @MotanReferer(
        basicReferer = "motanBasicReferer",
        group = "user-service",
        version = "2.0"
    )
    private UserService userServiceV2;
    
    public User getUserById(Long id, boolean useV2) {
        return useV2 ? userServiceV2.findById(id) : userServiceV1.findById(id);
    }
}
```

### 2.2 异步调用示例

#### 异步服务接口
```java
public interface AsyncUserService {
    CompletableFuture<User> findByIdAsync(Long id);
    CompletableFuture<List<User>> findByNameAsync(String name);
}
```

#### 异步服务实现
```java
@MotanService(basicService = "asyncServiceConfig")
@Service
public class AsyncUserServiceImpl implements AsyncUserService {
    
    @Async
    @Override
    public CompletableFuture<User> findByIdAsync(Long id) {
        // 模拟异步处理
        return CompletableFuture.supplyAsync(() -> {
            // 实际的数据库查询
            return userRepository.findById(id).orElse(null);
        });
    }
    
    @Async
    @Override
    public CompletableFuture<List<User>> findByNameAsync(String name) {
        return CompletableFuture.supplyAsync(() -> {
            return userRepository.findByNameContaining(name);
        });
    }
}

@Bean(name = "asyncServiceConfig")
public BasicServiceConfigBean asyncServiceConfig() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    config.setAsync(true);  // 启用异步
    return config;
}
```

#### 异步服务消费
```java
@Component
public class AsyncUserController {
    
    @MotanReferer(
        basicReferer = "asyncRefererConfig",
        async = true
    )
    private AsyncUserService asyncUserService;
    
    @GetMapping("/users/{id}/async")
    public CompletableFuture<ResponseEntity<User>> getUserAsync(@PathVariable Long id) {
        return asyncUserService.findByIdAsync(id)
            .thenApply(user -> user != null ? 
                ResponseEntity.ok(user) : ResponseEntity.notFound().build());
    }
}
```

### 2.3 自定义过滤器示例

#### 自定义日志过滤器
```java
@SpiMeta(name = "customLog")
@Activation(sequence = 100)
public class CustomLogFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomLogFilter.class);
    
    @Override
    public Response filter(Caller<?> caller, Request request) {
        long startTime = System.currentTimeMillis();
        String method = request.getInterfaceName() + "." + request.getMethodName();
        
        logger.info("开始调用: {}, 参数: {}", method, Arrays.toString(request.getArguments()));
        
        try {
            Response response = caller.call(request);
            long duration = System.currentTimeMillis() - startTime;
            
            if (response.getException() != null) {
                logger.error("调用失败: {}, 耗时: {}ms, 异常: {}", 
                    method, duration, response.getException().getMessage());
            } else {
                logger.info("调用成功: {}, 耗时: {}ms, 结果: {}", 
                    method, duration, response.getValue());
            }
            
            return response;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("调用异常: {}, 耗时: {}ms", method, duration, e);
            throw e;
        }
    }
}
```

#### 注册过滤器
```java
// 在META-INF/services/com.weibo.api.motan.filter.Filter文件中添加
com.example.filter.CustomLogFilter
```

#### 使用过滤器
```java
@Bean(name = "motanProtocol")
public ProtocolConfigBean protocolConfig() {
    ProtocolConfigBean config = new ProtocolConfigBean();
    config.setName("motan");
    config.setFilter("customLog,trace");  // 使用自定义过滤器
    return config;
}
```

## 3. 性能优化示例

### 3.1 高并发服务配置

```java
@Configuration
public class HighConcurrencyConfig {
    
    @Bean(name = "highConcurrencyProtocol")
    public ProtocolConfigBean highConcurrencyProtocol() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        
        // 基础配置
        config.setName("motan");
        config.setSerialization("hessian2");
        
        // 线程池优化
        config.setMinWorkerThread(100);      // 增加最小线程数
        config.setMaxWorkerThread(1000);     // 增加最大线程数
        config.setWorkerQueueSize(2000);     // 设置队列大小
        
        // 性能优化
        config.setRequestTimeout(3000);      // 较短超时
        config.setHeartbeat(30000);          // 心跳间隔
        config.setCompress(true);            // 启用压缩
        config.setMaxContentLength(50 * 1024 * 1024); // 增加最大内容长度
        
        return config;
    }
    
    @Bean(name = "highConcurrencyService")
    public BasicServiceConfigBean highConcurrencyService() {
        BasicServiceConfigBean config = new BasicServiceConfigBean();
        
        config.setProtocol("highConcurrencyProtocol");
        config.setRegistry("motanRegistry");
        
        // 并发控制
        config.setActives(500);              // 限制最大并发
        config.setShareChannel(true);        // 共享通道
        config.setRequestTimeout(3000);
        config.setRetries(0);                // 不重试，避免重复处理
        
        // 负载均衡优化
        config.setLoadbalance("activeWeight"); // 活跃度加权
        config.setHaStrategy("failfast");     // 快速失败
        
        return config;
    }
}
```

### 3.2 连接池优化配置

```java
@Bean(name = "optimizedReferer")
public BasicRefererConfigBean optimizedReferer() {
    BasicRefererConfigBean config = new BasicRefererConfigBean();
    
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    
    // 连接池优化
    config.setMinClientConnection(10);       // 增加最小连接数
    config.setMaxClientConnection(50);       // 增加最大连接数
    config.setShareChannel(true);            // 共享通道
    
    // 超时和重试优化
    config.setRequestTimeout(5000);
    config.setRetries(1);                    // 适度重试
    
    // 负载均衡
    config.setLoadbalance("activeWeight");
    config.setCluster("failover");
    
    return config;
}
```

### 3.3 批量操作优化

#### 批量服务接口
```java
public interface BatchUserService {
    List<User> batchFindById(List<Long> ids);
    Map<Long, User> mapFindById(List<Long> ids);
    BatchResult batchSave(List<User> users);
    BatchResult batchDelete(List<Long> ids);
}

public class BatchResult {
    private int successCount;
    private int failCount;
    private List<String> errors;
    // getters and setters
}
```

#### 批量服务实现
```java
@MotanService(basicService = "batchServiceConfig")
@Service
public class BatchUserServiceImpl implements BatchUserService {
    
    @Override
    public List<User> batchFindById(List<Long> ids) {
        // 使用IN查询优化
        return userRepository.findAllById(ids);
    }
    
    @Override
    public Map<Long, User> mapFindById(List<Long> ids) {
        List<User> users = userRepository.findAllById(ids);
        return users.stream().collect(Collectors.toMap(User::getId, u -> u));
    }
    
    @Override
    @Transactional
    public BatchResult batchSave(List<User> users) {
        BatchResult result = new BatchResult();
        List<String> errors = new ArrayList<>();
        
        for (User user : users) {
            try {
                userRepository.save(user);
                result.setSuccessCount(result.getSuccessCount() + 1);
            } catch (Exception e) {
                result.setFailCount(result.getFailCount() + 1);
                errors.add("保存用户失败: " + user.getName() + ", 错误: " + e.getMessage());
            }
        }
        
        result.setErrors(errors);
        return result;
    }
}
```

## 4. 故障处理示例

### 4.1 熔断降级配置

```java
@Component
public class CircuitBreakerUserService {
    
    @MotanReferer(
        basicReferer = "circuitBreakerReferer",
        requestTimeout = 3000,
        retries = 1
    )
    private UserService userService;
    
    @HystrixCommand(
        fallbackMethod = "getUserFallback",
        commandProperties = {
            @HystrixProperty(name = "circuitBreaker.enabled", value = "true"),
            @HystrixProperty(name = "circuitBreaker.requestVolumeThreshold", value = "10"),
            @HystrixProperty(name = "circuitBreaker.errorThresholdPercentage", value = "50"),
            @HystrixProperty(name = "circuitBreaker.sleepWindowInMilliseconds", value = "10000")
        }
    )
    public User getUserWithCircuitBreaker(Long id) {
        return userService.findById(id);
    }
    
    public User getUserFallback(Long id) {
        // 降级处理：返回默认用户或缓存数据
        User fallbackUser = new User();
        fallbackUser.setId(id);
        fallbackUser.setName("服务暂时不可用");
        return fallbackUser;
    }
}
```

### 4.2 重试机制配置

```java
@Component
public class RetryUserService {
    
    @MotanReferer(
        basicReferer = "retryReferer",
        retries = 3,
        requestTimeout = 5000
    )
    private UserService userService;
    
    @Retryable(
        value = {Exception.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public User getUserWithRetry(Long id) {
        try {
            return userService.findById(id);
        } catch (Exception e) {
            logger.warn("获取用户失败，将重试: {}", e.getMessage());
            throw e;
        }
    }
    
    @Recover
    public User recover(Exception e, Long id) {
        logger.error("重试失败，执行恢复逻辑: {}", e.getMessage());
        return null;
    }
}
```

### 4.3 健康检查实现

```java
@Component
public class MotanHealthIndicator implements HealthIndicator {
    
    @MotanReferer(basicReferer = "motanBasicReferer")
    private UserService userService;
    
    @Override
    public Health health() {
        try {
            // 执行健康检查调用
            userService.findById(1L);
            return Health.up()
                .withDetail("motan", "UserService is available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("motan", "UserService is unavailable")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## 5. 监控集成示例

### 5.1 Prometheus监控集成

```java
@Component
public class MotanMetricsFilter implements Filter {
    
    private final Counter requestCounter = Counter.build()
        .name("motan_requests_total")
        .help("Total number of requests")
        .labelNames("service", "method", "status")
        .register();
    
    private final Histogram requestDuration = Histogram.build()
        .name("motan_request_duration_seconds")
        .help("Request duration in seconds")
        .labelNames("service", "method")
        .register();
    
    @Override
    public Response filter(Caller<?> caller, Request request) {
        String service = request.getInterfaceName();
        String method = request.getMethodName();
        
        Timer.Sample sample = Timer.start();
        
        try {
            Response response = caller.call(request);
            
            // 记录成功请求
            requestCounter.labels(service, method, "success").inc();
            
            return response;
        } catch (Exception e) {
            // 记录失败请求
            requestCounter.labels(service, method, "error").inc();
            throw e;
        } finally {
            // 记录请求耗时
            sample.stop(requestDuration.labels(service, method));
        }
    }
}
```

### 5.2 日志集成示例

```java
@Aspect
@Component
public class MotanLoggingAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(MotanLoggingAspect.class);
    
    @Around("@annotation(com.weibo.api.motan.config.springsupport.annotation.MotanReferer)")
    public Object logMotanCall(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        logger.info("调用Motan服务: {}, 参数: {}", methodName, Arrays.toString(args));
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;
            
            logger.info("Motan服务调用成功: {}, 耗时: {}ms", methodName, duration);
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("Motan服务调用失败: {}, 耗时: {}ms, 错误: {}", 
                methodName, duration, e.getMessage());
            throw e;
        }
    }
}
```

通过这些实践示例，您可以根据具体的业务场景和需求，灵活配置和使用Motan RPC框架，实现高性能、高可用的分布式服务架构。

# Motan配置Bean完整参考手册

本文档提供Motan RPC框架所有配置Bean的完整参数说明和使用示例。

## 目录
1. [AnnotationBean配置](#1-annotationbean配置)
2. [ProtocolConfigBean配置](#2-protocolconfigbean配置)
3. [RegistryConfigBean配置](#3-registryconfigbean配置)
4. [BasicServiceConfigBean配置](#4-basicserviceconfigbean配置)
5. [BasicRefererConfigBean配置](#5-basicrefererconfigbean配置)
6. [配置组合示例](#6-配置组合示例)

## 1. AnnotationBean配置

### 基本配置
```java
@Bean
public AnnotationBean motanAnnotationBean() {
    AnnotationBean bean = new AnnotationBean();
    bean.setPackage("com.example.service");     // 必需：扫描包路径
    bean.setApplication("my-app");              // 可选：应用名称
    bean.setModule("user-module");              // 可选：模块名称
    return bean;
}
```

### 完整参数表

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| package | String | 无 | 扫描包路径，支持多个包用逗号分隔 |
| application | String | 无 | 应用名称，用于服务治理 |
| module | String | 无 | 模块名称，用于服务分组 |

### 使用示例
```java
// 扫描多个包
bean.setPackage("com.example.service,com.example.api");

// 设置应用信息
bean.setApplication("user-service");
bean.setModule("user-management");
```

## 2. ProtocolConfigBean配置

### 基本配置
```java
@Bean(name = "motanProtocol")
public ProtocolConfigBean protocolConfig() {
    ProtocolConfigBean config = new ProtocolConfigBean();
    config.setName("motan");
    config.setPort(8002);
    return config;
}
```

### 完整参数表

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| **基础配置** |
| name | String | motan | 协议名称：motan, injvm |
| port | int | 0 | 服务端口，0表示随机端口 |
| host | String | 本机IP | 服务主机地址 |
| **序列化配置** |
| serialization | String | hessian2 | 序列化方式：hessian2, fastjson, java |
| codec | String | motan | 编解码器：motan, grpc |
| **传输配置** |
| transporter | String | netty | 传输层：netty, mina |
| endpointFactory | String | motan | 端点工厂：motan, grpc |
| **线程池配置** |
| minWorkerThread | int | 20 | 最小工作线程数 |
| maxWorkerThread | int | 200 | 最大工作线程数 |
| workerQueueSize | int | 0 | 工作队列大小，0表示无界队列 |
| **性能配置** |
| requestTimeout | int | 200 | 请求超时时间(毫秒) |
| heartbeat | int | 0 | 心跳间隔(毫秒)，0表示不发送心跳 |
| maxContentLength | int | 10485760 | 最大内容长度(字节) |
| **高级配置** |
| compress | boolean | false | 是否启用压缩 |
| filter | String | 无 | 过滤器链，多个用逗号分隔 |
| buffer | int | 8192 | 缓冲区大小 |
| payload | int | 8388608 | 负载大小限制 |

### 配置示例

#### 高性能配置
```java
@Bean(name = "highPerformanceProtocol")
public ProtocolConfigBean highPerformanceProtocol() {
    ProtocolConfigBean config = new ProtocolConfigBean();
    
    // 基础配置
    config.setName("motan");
    config.setPort(8002);
    config.setSerialization("hessian2");
    
    // 线程池优化
    config.setMinWorkerThread(50);
    config.setMaxWorkerThread(500);
    config.setWorkerQueueSize(1000);
    
    // 性能优化
    config.setRequestTimeout(3000);
    config.setHeartbeat(30000);
    config.setCompress(true);
    config.setMaxContentLength(20 * 1024 * 1024);
    
    return config;
}
```

#### 开发调试配置
```java
@Bean(name = "debugProtocol")
public ProtocolConfigBean debugProtocol() {
    ProtocolConfigBean config = new ProtocolConfigBean();
    
    config.setName("motan");
    config.setPort(8002);
    config.setSerialization("fastjson");  // JSON便于调试
    config.setRequestTimeout(30000);      // 长超时便于调试
    config.setCompress(false);            // 关闭压缩便于抓包
    config.setFilter("trace,accessLog");  // 开启调试过滤器
    
    return config;
}
```

## 3. RegistryConfigBean配置

### 基本配置
```java
@Bean(name = "motanRegistry")
public RegistryConfigBean registryConfig() {
    RegistryConfigBean config = new RegistryConfigBean();
    config.setAddress("127.0.0.1:2181");
    config.setRegProtocol("zookeeper");
    return config;
}
```

### 完整参数表

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| **基础配置** |
| address | String | 无 | 注册中心地址，支持集群 |
| regProtocol | String | 无 | 注册中心协议：zookeeper, consul, direct |
| **超时配置** |
| connectTimeout | int | 1000 | 连接超时时间(毫秒) |
| requestTimeout | int | 200 | 请求超时时间(毫秒) |
| registrySessionTimeout | int | 60000 | 会话超时时间(毫秒) |
| registryRetryPeriod | int | 30000 | 重试周期(毫秒) |
| **行为配置** |
| check | boolean | true | 是否检查注册中心可用性 |
| register | boolean | true | 是否注册服务 |
| subscribe | boolean | true | 是否订阅服务 |
| dynamic | boolean | true | 是否动态注册 |
| **认证配置** |
| username | String | 无 | 用户名 |
| password | String | 无 | 密码 |

### 配置示例

#### Zookeeper集群配置
```java
@Bean(name = "zkClusterRegistry")
public RegistryConfigBean zkClusterRegistry() {
    RegistryConfigBean config = new RegistryConfigBean();
    
    // 集群地址
    config.setAddress("zk1:2181,zk2:2181,zk3:2181");
    config.setRegProtocol("zookeeper");
    
    // 超时配置
    config.setConnectTimeout(5000);
    config.setRequestTimeout(3000);
    config.setRegistrySessionTimeout(30000);
    config.setRegistryRetryPeriod(10000);
    
    // 行为配置
    config.setCheck(true);
    config.setRegister(true);
    config.setSubscribe(true);
    
    return config;
}
```

#### Consul配置
```java
@Bean(name = "consulRegistry")
public RegistryConfigBean consulRegistry() {
    RegistryConfigBean config = new RegistryConfigBean();
    
    config.setAddress("consul-server:8500");
    config.setRegProtocol("consul");
    config.setConnectTimeout(3000);
    config.setRequestTimeout(2000);
    
    return config;
}
```

#### 直连模式配置
```java
@Bean(name = "directRegistry")
public RegistryConfigBean directRegistry() {
    RegistryConfigBean config = new RegistryConfigBean();
    
    // 直连模式，直接指定服务提供者地址
    config.setAddress("*************:8002,*************:8002");
    config.setRegProtocol("direct");
    config.setCheck(false);
    
    return config;
}
```

## 4. BasicServiceConfigBean配置

### 基本配置
```java
@Bean(name = "motanBasicService")
public BasicServiceConfigBean basicServiceConfig() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    return config;
}
```

### 完整参数表

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| **关联配置** |
| protocol | String | 无 | 关联的协议配置Bean名称 |
| registry | String | 无 | 关联的注册中心配置Bean名称 |
| **服务标识** |
| group | String | default_rpc | 服务分组 |
| version | String | 1.0 | 服务版本 |
| application | String | 无 | 应用名称 |
| module | String | 无 | 模块名称 |
| **性能配置** |
| shareChannel | boolean | false | 是否共享通道 |
| actives | int | 0 | 最大并发数，0表示不限制 |
| requestTimeout | int | 200 | 请求超时时间(毫秒) |
| retries | int | 0 | 重试次数 |
| **治理配置** |
| loadbalance | String | activeWeight | 负载均衡策略 |
| haStrategy | String | failover | 高可用策略 |
| cluster | String | default | 集群策略 |
| **行为配置** |
| async | boolean | false | 是否异步 |
| accessLog | boolean | false | 是否记录访问日志 |
| check | boolean | true | 是否检查依赖 |
| register | boolean | true | 是否注册服务 |
| **过滤器配置** |
| filter | String | 无 | 过滤器链 |

### 配置示例

#### 高并发服务配置
```java
@Bean(name = "highConcurrencyService")
public BasicServiceConfigBean highConcurrencyService() {
    BasicServiceConfigBean config = new BasicServiceConfigBean();
    
    // 关联配置
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    
    // 服务标识
    config.setGroup("high-concurrency");
    config.setVersion("2.0");
    config.setApplication("payment-service");
    
    // 性能配置
    config.setShareChannel(true);
    config.setActives(1000);              // 限制并发数
    config.setRequestTimeout(5000);
    config.setRetries(0);                 // 不重试避免重复处理
    
    // 治理配置
    config.setLoadbalance("activeWeight");
    config.setHaStrategy("failfast");     // 快速失败
    
    return config;
}
```

## 5. BasicRefererConfigBean配置

### 基本配置
```java
@Bean(name = "motanBasicReferer")
public BasicRefererConfigBean basicRefererConfig() {
    BasicRefererConfigBean config = new BasicRefererConfigBean();
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    return config;
}
```

### 完整参数表

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| **关联配置** |
| protocol | String | 无 | 关联的协议配置Bean名称 |
| registry | String | 无 | 关联的注册中心配置Bean名称 |
| **服务标识** |
| group | String | default_rpc | 服务分组 |
| version | String | 1.0 | 服务版本 |
| application | String | 无 | 应用名称 |
| module | String | 无 | 模块名称 |
| **性能配置** |
| shareChannel | boolean | false | 是否共享通道 |
| actives | int | 0 | 最大并发数，0表示不限制 |
| requestTimeout | int | 200 | 请求超时时间(毫秒) |
| retries | int | 0 | 重试次数 |
| **连接池配置** |
| minClientConnection | int | 2 | 最小连接数 |
| maxClientConnection | int | 10 | 最大连接数 |
| **治理配置** |
| loadbalance | String | activeWeight | 负载均衡策略 |
| haStrategy | String | failover | 高可用策略 |
| cluster | String | failover | 集群容错策略 |
| **行为配置** |
| async | boolean | false | 是否异步调用 |
| callback | boolean | false | 是否启用回调 |
| accessLog | boolean | false | 是否记录访问日志 |
| check | boolean | true | 是否检查提供者 |
| lazy | boolean | false | 是否延迟初始化 |
| **过滤器配置** |
| filter | String | 无 | 过滤器链 |

### 配置示例

#### 高可用客户端配置
```java
@Bean(name = "highAvailabilityReferer")
public BasicRefererConfigBean highAvailabilityReferer() {
    BasicRefererConfigBean config = new BasicRefererConfigBean();
    
    // 关联配置
    config.setProtocol("motanProtocol");
    config.setRegistry("motanRegistry");
    
    // 服务标识
    config.setGroup("core-service");
    config.setVersion("1.0");
    config.setApplication("web-portal");
    
    // 性能配置
    config.setRequestTimeout(3000);
    config.setRetries(2);                 // 重试2次
    
    // 连接池配置
    config.setMinClientConnection(5);
    config.setMaxClientConnection(20);
    
    // 治理配置
    config.setLoadbalance("activeWeight");
    config.setCluster("failover");        // 故障转移
    config.setHaStrategy("failover");
    
    // 行为配置
    config.setCheck(true);
    config.setAccessLog(true);
    
    return config;
}
```

## 6. 配置组合示例

### 6.1 微服务架构配置

```java
@Configuration
public class MicroserviceMotanConfig {
    
    // 用户服务配置
    @Bean(name = "userServiceConfig")
    public BasicServiceConfigBean userServiceConfig() {
        BasicServiceConfigBean config = new BasicServiceConfigBean();
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        config.setGroup("user-service");
        config.setVersion("1.0");
        config.setApplication("user-management");
        return config;
    }
    
    // 订单服务配置
    @Bean(name = "orderServiceConfig")
    public BasicServiceConfigBean orderServiceConfig() {
        BasicServiceConfigBean config = new BasicServiceConfigBean();
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        config.setGroup("order-service");
        config.setVersion("1.0");
        config.setApplication("order-management");
        return config;
    }
    
    // 用户服务引用配置
    @Bean(name = "userServiceReferer")
    public BasicRefererConfigBean userServiceReferer() {
        BasicRefererConfigBean config = new BasicRefererConfigBean();
        config.setProtocol("motanProtocol");
        config.setRegistry("motanRegistry");
        config.setGroup("user-service");
        config.setVersion("1.0");
        config.setRequestTimeout(5000);
        config.setRetries(1);
        return config;
    }
}
```

### 6.2 多环境配置

```java
@Configuration
@Profile("prod")
public class ProductionMotanConfig {
    
    @Bean(name = "prodProtocol")
    public ProtocolConfigBean prodProtocol() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        config.setName("motan");
        config.setSerialization("hessian2");
        config.setMinWorkerThread(100);
        config.setMaxWorkerThread(1000);
        config.setRequestTimeout(3000);
        config.setCompress(true);
        return config;
    }
    
    @Bean(name = "prodRegistry")
    public RegistryConfigBean prodRegistry() {
        RegistryConfigBean config = new RegistryConfigBean();
        config.setAddress("prod-zk1:2181,prod-zk2:2181,prod-zk3:2181");
        config.setRegProtocol("zookeeper");
        config.setConnectTimeout(3000);
        config.setRegistrySessionTimeout(30000);
        return config;
    }
}

@Configuration
@Profile("dev")
public class DevelopmentMotanConfig {
    
    @Bean(name = "devProtocol")
    public ProtocolConfigBean devProtocol() {
        ProtocolConfigBean config = new ProtocolConfigBean();
        config.setName("motan");
        config.setSerialization("fastjson");
        config.setRequestTimeout(30000);
        config.setCompress(false);
        config.setFilter("trace,accessLog");
        return config;
    }
    
    @Bean(name = "devRegistry")
    public RegistryConfigBean devRegistry() {
        RegistryConfigBean config = new RegistryConfigBean();
        config.setAddress("dev-zk:2181");
        config.setRegProtocol("zookeeper");
        config.setCheck(false);
        return config;
    }
}
```

通过本参考手册，您可以根据具体需求灵活配置Motan RPC框架的各个组件，实现最佳的性能和可靠性。

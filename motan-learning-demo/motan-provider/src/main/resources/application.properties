# Motan Provider ????
# ??CAF?????????

# ??????
spring.application.name=motan-provider
server.port=8080

# ????
app.logging.path=logs/motan-provider
logging.level.com.example.motan=INFO
logging.level.com.weibo.api.motan=INFO

# Motan RPC ????
# ???????
app.motan.user.annotation.package=com.example.motan.provider.service

# ?????? - ??Zookeeper
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.registry.reg-protocol=zookeeper
app.motan.user.registry.request-timeout=1000
app.motan.user.registry.connect-timeout=3000

# ????
app.motan.user.protocol.name=motan
app.motan.user.protocol.min-worker-thread=20
app.motan.user.protocol.max-worker-thread=200
# ????????? + ???? + ????
app.motan.user.protocol.filter=cafTracing,pepperProfiler,sentinelProfiler
# ??????????
app.motan.user.protocol.ha-strategy=failover

# ??????
app.motan.user.port=10010
app.motan.user.basic-service.check=false
app.motan.user.basic-service.share-channel=true
app.motan.user.basic-service.request-timeout=30000
app.motan.user.basic-service.application=motan-provider
app.motan.user.basic-service.group=user-service
app.motan.user.basic-service.version=1.0.0

# Sentinel ??????
app.motan.sentinel.enable=true

# ????
app.monitor.prometheus.port=9090

# ??????
spring.profiles.active=dev

# ===== ?????? (??) =====

# ??????
# app.motan.user.protocol.loadbalance=activeWeight
# ???: random, roundrobin, activeWeight, consistent, localFirst

# ??????  
# app.motan.user.protocol.cluster=failover
# ???: failover, failfast, failback, forking

# ????
# app.motan.user.basic-service.retries=2

# ????
# app.motan.user.basic-service.actives=0

# ?????
# app.motan.user.protocol.serialization=hessian2
# ???: hessian2, fastjson, java

# ????
# app.motan.user.protocol.compress=false

# ????(??)
# app.motan.user.protocol.heartbeat=60000

# ===== ????????? =====
# ??????????????????????

# Order????????
# app.motan.order.annotation.package=com.example.motan.provider.order
# app.motan.order.port=10011
# app.motan.order.registry.address=127.0.0.1:2181
# app.motan.order.basic-service.group=order-service

# Payment????????  
# app.motan.payment.annotation.package=com.example.motan.provider.payment
# app.motan.payment.port=10012
# app.motan.payment.registry.address=127.0.0.1:2181
# app.motan.payment.basic-service.group=payment-service

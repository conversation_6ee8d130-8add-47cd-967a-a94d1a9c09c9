package com.example.motan.provider;

import com.coohua.caf.core.rpc.EnableMotan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Motan服务提供者启动类
 * 
 * 关键配置说明：
 * @EnableMotan - 启用Motan RPC服务
 * namespace - 命名空间，用于服务隔离
 * 
 * 多命名空间示例：
 * @EnableMotan(namespace = "user")
 * @EnableMotan(namespace = "order") 
 * 
 * 这样可以在不同端口暴露不同的服务组
 */
@SpringBootApplication
@EnableMotan(namespace = "user")
public class MotanProviderApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MotanProviderApplication.class, args);
        System.out.println("=================================");
        System.out.println("Motan Provider 启动成功!");
        System.out.println("服务命名空间: user");
        System.out.println("=================================");
    }
}

package com.example.motan.consumer;

import com.coohua.caf.core.rpc.EnableMotan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Motan服务消费者启动类
 * 
 * 关键配置说明：
 * @EnableMotan - 启用Motan RPC客户端
 * namespace - 必须与Provider的命名空间一致
 * 
 * 消费者配置要点：
 * 1. 命名空间要与提供者匹配
 * 2. 注册中心地址要一致
 * 3. 服务分组和版本要匹配
 */
@SpringBootApplication
@EnableMotan(namespace = "user")
public class MotanConsumerApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(MotanConsumerApplication.class, args);
        System.out.println("=================================");
        System.out.println("Motan Consumer 启动成功!");
        System.out.println("服务命名空间: user");
        System.out.println("Web接口: http://localhost:8081");
        System.out.println("=================================");
    }
}

# Motan Consumer ????
# ??CAF?????????

# ??????
spring.application.name=motan-consumer
server.port=8081

# ????
app.logging.path=logs/motan-consumer
logging.level.com.example.motan=INFO
logging.level.com.weibo.api.motan=INFO

# Motan RPC ?????
# ???????
app.motan.user.annotation.package=com.example.motan.consumer.service

# ?????? - ???Provider??
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.registry.reg-protocol=zookeeper
app.motan.user.registry.request-timeout=1000
app.motan.user.registry.connect-timeout=3000

# ????
app.motan.user.protocol.name=motan
# ????????? + ???? + ????
app.motan.user.protocol.filter=cafTracing,pepperProfiler,sentinelProfiler
# ??????????
app.motan.user.protocol.ha-strategy=failover

# ??????
app.motan.user.basic-referer.check=false
app.motan.user.basic-referer.share-channel=true
app.motan.user.basic-referer.request-timeout=20000
app.motan.user.basic-referer.application=motan-consumer
app.motan.user.basic-referer.group=user-service
app.motan.user.basic-referer.version=1.0.0

# ????
app.monitor.prometheus.port=9091

# ??????
spring.profiles.active=dev

# ===== ?????? (??) =====

# ??????
# app.motan.user.basic-referer.loadbalance=activeWeight
# ???: random, roundrobin, activeWeight, consistent, localFirst

# ??????
# app.motan.user.basic-referer.cluster=failover
# ???: failover, failfast, failback, forking

# ????
# app.motan.user.basic-referer.retries=2

# ????
# app.motan.user.basic-referer.actives=0

# ??????
# app.motan.user.basic-referer.async=false

# ????
# app.motan.user.basic-referer.callback=false

# ?????
# app.motan.user.basic-referer.min-client-connection=2
# app.motan.user.basic-referer.max-client-connection=10

# ===== ????????? =====
# ?????????????????????

# Order??????
# app.motan.order.annotation.package=com.example.motan.consumer.order
# app.motan.order.registry.address=127.0.0.1:2181
# app.motan.order.basic-referer.group=order-service

# Payment??????
# app.motan.payment.annotation.package=com.example.motan.consumer.payment
# app.motan.payment.registry.address=127.0.0.1:2181
# app.motan.payment.basic-referer.group=payment-service

# ===== ?????? =====

# ????
# app.motan.user.registry.connect-timeout=3000

# ????
# app.motan.user.basic-referer.request-timeout=5000

# ????
# app.motan.user.protocol.heartbeat=60000

# ?????
# app.motan.user.protocol.serialization=hessian2

# ????
# app.motan.user.protocol.compress=false

# ===== ??????? =====

# ????
# app.motan.user.basic-referer.access-log=true

# ????
# app.motan.user.basic-referer.statistics=true

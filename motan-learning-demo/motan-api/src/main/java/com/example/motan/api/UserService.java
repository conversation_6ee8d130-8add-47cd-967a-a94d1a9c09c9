package com.example.motan.api;

import java.util.List;

/**
 * 用户服务接口 - Motan RPC服务定义
 * 
 * 核心概念：
 * 1. 服务接口是Provider和Consumer之间的契约
 * 2. 接口应该保持向后兼容性
 * 3. 方法参数和返回值必须可序列化
 */
public interface UserService {
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);
    
    /**
     * 创建新用户
     * @param user 用户信息
     * @return 创建成功的用户ID
     */
    Long createUser(User user);
    
    /**
     * 获取所有用户列表
     * @return 用户列表
     */
    List<User> getAllUsers();
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 是否更新成功
     */
    boolean updateUser(User user);
    
    /**
     * 删除用户
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);
    
    /**
     * 批量获取用户信息 - 演示复杂参数
     * @param userIds 用户ID列表
     * @return 用户信息映射
     */
    List<User> batchGetUsers(List<Long> userIds);
    
    /**
     * 用户登录验证
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    LoginResult login(String username, String password);
    
    /**
     * 获取用户统计信息 - 演示异常处理
     * @param userId 用户ID
     * @return 统计信息
     * @throws UserNotFoundException 用户不存在异常
     */
    UserStats getUserStats(Long userId) throws UserNotFoundException;
    
    /**
     * 异步获取用户信息 - 演示异步调用
     * @param userId 用户ID
     * @return 用户信息（异步返回）
     */
    User getUserByIdAsync(Long userId);
    
    /**
     * 健康检查接口
     * @return 服务状态
     */
    String healthCheck();
}

package com.example.motan.api;

/**
 * 用户不存在异常
 * 
 * Motan异常处理：
 * 1. 业务异常应该继承RuntimeException
 * 2. 异常类必须实现Serializable
 * 3. 异常会在网络间传输，要考虑序列化性能
 */
public class UserNotFoundException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private Long userId;
    
    public UserNotFoundException(Long userId) {
        super("User not found with id: " + userId);
        this.userId = userId;
    }
    
    public UserNotFoundException(String message) {
        super(message);
    }
    
    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
}

package com.example.motan.api;

import java.io.Serializable;

/**
 * 用户统计信息
 */
public class UserStats implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Long userId;
    private Integer loginCount;
    private Integer orderCount;
    private Double totalAmount;
    private String lastLoginTime;
    
    public UserStats() {}
    
    public UserStats(Long userId, Integer loginCount, Integer orderCount, Double totalAmount) {
        this.userId = userId;
        this.loginCount = loginCount;
        this.orderCount = orderCount;
        this.totalAmount = totalAmount;
    }
    
    // Getters and Setters
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getLoginCount() {
        return loginCount;
    }
    
    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }
    
    public Integer getOrderCount() {
        return orderCount;
    }
    
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }
    
    public Double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    @Override
    public String toString() {
        return "UserStats{" +
                "userId=" + userId +
                ", loginCount=" + loginCount +
                ", orderCount=" + orderCount +
                ", totalAmount=" + totalAmount +
                ", lastLoginTime='" + lastLoginTime + '\'' +
                '}';
    }
}

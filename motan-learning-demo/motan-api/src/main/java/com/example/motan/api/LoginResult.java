package com.example.motan.api;

import java.io.Serializable;

/**
 * 登录结果类
 */
public class LoginResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private boolean success;
    private String token;
    private String message;
    private User user;
    
    public LoginResult() {}
    
    public LoginResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public LoginResult(boolean success, String token, String message, User user) {
        this.success = success;
        this.token = token;
        this.message = message;
        this.user = user;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    @Override
    public String toString() {
        return "LoginResult{" +
                "success=" + success +
                ", token='" + token + '\'' +
                ", message='" + message + '\'' +
                ", user=" + user +
                '}';
    }
}

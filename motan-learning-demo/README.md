# Motan RPC 学习示例

基于CAF框架的Motan RPC完整学习示例，包含服务提供者、消费者和完整的配置。

## 项目结构

```
motan-learning-demo/
├── motan-api/                    # 服务接口定义
│   └── src/main/java/com/example/motan/api/
│       ├── UserService.java     # 用户服务接口
│       ├── User.java            # 用户实体
│       ├── LoginResult.java     # 登录结果
│       ├── UserStats.java       # 用户统计
│       └── UserNotFoundException.java # 业务异常
├── motan-provider/              # 服务提供者
│   ├── src/main/java/com/example/motan/provider/
│   │   ├── MotanProviderApplication.java # 启动类
│   │   └── service/
│   │       └── UserServiceImpl.java     # 服务实现
│   └── src/main/resources/
│       └── application.properties       # 配置文件
└── motan-consumer/              # 服务消费者
    ├── src/main/java/com/example/motan/consumer/
    │   ├── MotanConsumerApplication.java # 启动类
    │   ├── controller/
    │   │   └── UserController.java      # Web控制器
    │   └── service/
    │       └── UserServiceClient.java   # 服务客户端
    └── src/main/resources/
        └── application.properties       # 配置文件
```

## 核心特性演示

### 1. 基础RPC调用
- 服务接口定义和实现
- @MotanService 和 @MotanReferer 注解使用
- 基本的CRUD操作

### 2. 高级特性
- **熔断降级**: 集成Sentinel，支持降级和熔断
- **负载均衡**: 支持多种负载均衡策略
- **链路追踪**: 集成分布式追踪
- **异常处理**: 完整的异常传播和处理

### 3. 配置管理
- **命名空间隔离**: 支持多服务实例
- **配置外部化**: 基于Spring Boot配置
- **注册中心**: Zookeeper集成

## 快速开始

### 前置条件
1. JDK 8+
2. Maven 3.6+
3. Zookeeper 3.4+

### 启动步骤

1. **启动Zookeeper**
```bash
# 下载并启动Zookeeper
zkServer.sh start
```

2. **启动服务提供者**
```bash
cd motan-provider
mvn spring-boot:run
```

3. **启动服务消费者**
```bash
cd motan-consumer
mvn spring-boot:run
```

4. **测试服务调用**
```bash
# 获取用户信息
curl http://localhost:8081/api/users/1

# 创建用户
curl -X POST http://localhost:8081/api/users \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","age":25}'

# 健康检查
curl http://localhost:8081/api/users/health
```

## 核心概念学习

### 1. 服务接口设计
```java
public interface UserService {
    User getUserById(Long userId);
    Long createUser(User user);
    // ... 其他方法
}
```

**要点:**
- 接口方法参数和返回值必须可序列化
- 避免使用复杂的泛型和继承关系
- 考虑向后兼容性

### 2. 服务提供者
```java
@MotanService
@Service
public class UserServiceImpl implements UserService {
    
    @SentinelResource(value = "getUserById", blockHandler = "getUserByIdBlock")
    public User getUserById(Long userId) {
        // 业务逻辑
    }
    
    // 熔断降级方法
    public User getUserByIdBlock(Long userId, BlockException ex) {
        // 降级逻辑
    }
}
```

**要点:**
- @MotanService 标记服务实现
- 集成Sentinel实现熔断降级
- 提供完整的异常处理

### 3. 服务消费者
```java
@Component
public class UserServiceClient {
    
    @MotanReferer
    private UserService userService;
    
    public User getUserById(Long userId) {
        return userService.getUserById(userId);
    }
}
```

**要点:**
- @MotanReferer 注入服务引用
- 封装服务调用逻辑
- 处理网络异常和业务异常

### 4. 配置管理
```properties
# 服务提供者配置
app.motan.user.annotation.package=com.example.motan.provider.service
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.port=10010

# 服务消费者配置
app.motan.user.annotation.package=com.example.motan.consumer.service
app.motan.user.registry.address=127.0.0.1:2181
app.motan.user.basic-referer.request-timeout=20000
```

## 高级特性详解

### 1. 负载均衡策略
- **random**: 随机选择
- **roundrobin**: 轮询
- **activeWeight**: 活跃度加权
- **consistent**: 一致性哈希
- **localFirst**: 本地优先

### 2. 集群容错策略
- **failover**: 故障转移（默认）
- **failfast**: 快速失败
- **failback**: 失败恢复
- **forking**: 并行调用

### 3. 过滤器链
- **cafTracing**: 分布式链路追踪
- **pepperProfiler**: 性能监控
- **sentinelProfiler**: 熔断降级

### 4. 序列化方式
- **hessian2**: 默认，性能好
- **fastjson**: JSON格式
- **java**: Java原生序列化

## 监控和运维

### 1. 健康检查
```bash
curl http://localhost:8081/api/users/health
```

### 2. 性能监控
- Prometheus指标暴露
- 调用链路追踪
- 异常统计和告警

### 3. 配置管理
- 支持Apollo配置中心
- 配置热更新
- 多环境配置隔离

## 最佳实践

### 1. 接口设计
- 保持接口简洁和稳定
- 使用版本控制管理接口变更
- 避免频繁的接口修改

### 2. 异常处理
- 区分业务异常和系统异常
- 提供有意义的错误信息
- 实现优雅的降级策略

### 3. 性能优化
- 合理设置超时时间
- 使用连接池复用连接
- 选择合适的序列化方式

### 4. 运维监控
- 配置完整的监控指标
- 建立告警机制
- 定期进行性能测试

## 常见问题

### 1. 服务注册失败
- 检查Zookeeper连接
- 确认端口没有被占用
- 查看防火墙设置

### 2. 服务调用超时
- 调整请求超时时间
- 检查网络连接
- 分析服务端性能

### 3. 负载均衡不生效
- 确认多个服务实例已注册
- 检查负载均衡策略配置
- 验证服务分组和版本

## 扩展学习

1. **源码分析**: 深入理解Motan核心实现
2. **性能测试**: 进行压力测试和性能调优
3. **集成实践**: 与Spring Cloud、Dubbo等框架对比
4. **生产部署**: 学习生产环境的部署和运维

## 参考资料

- [Motan官方文档](https://github.com/weibocom/motan/wiki)
- [CAF框架文档](内部文档链接)
- [Sentinel文档](https://sentinelguard.io/zh-cn/)
- [Zookeeper文档](https://zookeeper.apache.org/)
